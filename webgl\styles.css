/**
 * 白云电气设备数字孪生系统 - 样式文件
 * 包含页面布局、Unity容器样式、图表容器样式等
 */

body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  background-color: #0a1a2a;
  color: #fff;
  font-family: "时尚中黑简体", "Microsoft YaHei", sans-serif;
}

#main-container {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
}

/* 顶部标题栏 */
#header {
  height: 60px;
  background: linear-gradient(90deg, rgba(0,30,60,0.8), rgba(0,60,120,0.8), rgba(0,30,60,0.8));
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.5);
  z-index: 100;
}

#title {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 0 10px rgba(0,150,255,0.8);
  letter-spacing: 2px;
}

#controls {
  display: flex;
  gap: 15px;
}

.control-btn {
  background: rgba(0,80,150,0.6);
  border: 1px solid rgba(0,150,255,0.6);
  color: #fff;
  padding: 6px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.control-btn:hover {
  background: rgba(0,120,200,0.8);
  box-shadow: 0 0 10px rgba(0,150,255,0.8);
}

/* 内容区域 */
#content {
  flex: 1;
  display: flex;
  position: relative;
}

/* Unity WebGL 嵌入容器 */
#unity-embed-container {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}

#unity-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: #0a1a2a;
}

/* 图表容器 */
.chart-container {
  position: absolute;
  background: rgba(0,20,40,0.7);
  border: 1px solid rgba(0,100,200,0.6);
  border-radius: 5px;
  box-shadow: 0 0 15px rgba(0,100,200,0.3);
  overflow: hidden;
  z-index: 10;
  display: none; /* 初始隐藏图表容器，等Unity加载完成后再显示 */
}

.chart-title {
  background: rgba(0,40,80,0.8);
  color: #fff;
  padding: 8px 12px;
  font-size: 14px;
  border-bottom: 1px solid rgba(0,100,200,0.6);
}

.chart {
  width: 100%;
  height: calc(100% - 35px);
}

#chart1 {
  top: 70px;
  left: 10px;
  width: 300px;
  height: 220px;
}

#chart2 {
  top: 70px;
  right: 10px;
  width: 300px;
  height: 220px;
}

#chart3 {
  bottom: 10px;
  left: 10px;
  width: 300px;
  height: 220px;
}

#chart4 {
  bottom: 10px;
  right: 10px;
  width: 300px;
  height: 220px;
}
