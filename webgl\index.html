<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>白云电气设备数字孪生系统</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="TemplateData/style.css">
    <link rel="stylesheet" href="styles.css">
    <link rel="manifest" href="manifest.webmanifest">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入 echarts 控制器 -->
    <script src="TemplateData/echarts-controller.js"></script>
    <!-- 引入图表配置文件 -->
    <script src="charts.js"></script>
  </head>
  <body>
    <div id="main-container">
      <!-- 顶部标题栏 -->
      <div id="header">
        <div id="title">白云电气设备数字孪生系统</div>
        <div id="controls">
          <button class="control-btn" id="overview-btn">总览</button>
          <button class="control-btn" id="tour-btn">漫游</button>
          <button class="control-btn" id="expand-btn">展开</button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div id="content">
        <!-- Unity WebGL 容器 -->
        <div id="unity-container">
          <canvas id="unity-canvas" width=3840 height=2160 tabindex="-1"></canvas>
          <div id="unity-loading-bar">
            <div id="unity-logo"></div>
            <div id="unity-progress-bar-empty">
              <div id="unity-progress-bar-full"></div>
            </div>
          </div>
          <div id="unity-warning"></div>
        </div>

        <!-- 图表容器 -->
        <div id="chart1" class="chart-container">
          <div class="chart-title">设备运行状态</div>
          <div id="chart1-content" class="chart"></div>
        </div>

        <div id="chart2" class="chart-container">
          <div class="chart-title">温度监测</div>
          <div id="chart2-content" class="chart"></div>
        </div>

        <div id="chart3" class="chart-container">
          <div class="chart-title">电压监测</div>
          <div id="chart3-content" class="chart"></div>
        </div>

        <div id="chart4" class="chart-container">
          <div class="chart-title">系统负载</div>
          <div id="chart4-content" class="chart"></div>
        </div>
      </div>
    </div>

    <!-- 引入主要JavaScript文件 -->
    <script src="main.js"></script>

  </body>
</html>
