<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>白云电气设备数字孪生系统</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="TemplateData/style.css">
    <link rel="manifest" href="manifest.webmanifest">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入 echarts 控制器 -->
    <script src="TemplateData/echarts-controller.js"></script>
    <style>
      body {
        margin: 0;
        padding: 0;
        overflow: hidden;
        background-color: #0a1a2a;
        color: #fff;
        font-family: "时尚中黑简体", "Microsoft YaHei", sans-serif;
      }
      
      #main-container {
        display: flex;
        flex-direction: column;
        width: 100vw;
        height: 100vh;
      }
      
      /* 顶部标题栏 */
      #header {
        height: 60px;
        background: linear-gradient(90deg, rgba(0,30,60,0.8), rgba(0,60,120,0.8), rgba(0,30,60,0.8));
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.5);
        z-index: 100;
      }
      
      #title {
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        text-shadow: 0 0 10px rgba(0,150,255,0.8);
        letter-spacing: 2px;
      }
      
      #controls {
        display: flex;
        gap: 15px;
      }
      
      .control-btn {
        background: rgba(0,80,150,0.6);
        border: 1px solid rgba(0,150,255,0.6);
        color: #fff;
        padding: 6px 15px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
      }
      
      .control-btn:hover {
        background: rgba(0,120,200,0.8);
        box-shadow: 0 0 10px rgba(0,150,255,0.8);
      }
      
      /* 内容区域 */
      #content {
        flex: 1;
        display: flex;
        position: relative;
      }
      
      /* Unity WebGL 容器 */
      #unity-container {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
      }
      
      #unity-canvas {
        width: 100%;
        height: 100%;
        background: #0a1a2a;
      }
      
      #unity-loading-bar {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        display: none;
      }
      
      #unity-logo {
        width: 154px;
        height: 130px;
        background: url('TemplateData/unity-logo-dark.png') no-repeat center;
      }
      
      #unity-progress-bar-empty {
        margin-left: auto;
        margin-right: auto;
        width: 141px;
        height: 18px;
        margin-top: 10px;
        background: url('TemplateData/progress-bar-empty-dark.png') no-repeat center;
      }
      
      #unity-progress-bar-full {
        width: 0%;
        height: 18px;
        margin-top: 10px;
        background: url('TemplateData/progress-bar-full-dark.png') no-repeat center;
      }
      
      #unity-warning {
        position: absolute;
        left: 50%;
        top: 5%;
        transform: translate(-50%);
        background: white;
        padding: 10px;
        display: none;
      }
      
      /* 图表容器 */
      .chart-container {
        position: absolute;
        background: rgba(0,20,40,0.7);
        border: 1px solid rgba(0,100,200,0.6);
        border-radius: 5px;
        box-shadow: 0 0 15px rgba(0,100,200,0.3);
        overflow: hidden;
        z-index: 10;
        display: none; /* 初始隐藏图表容器，等Unity加载完成后再显示 */
      }
      
      .chart-title {
        background: rgba(0,40,80,0.8);
        color: #fff;
        padding: 8px 12px;
        font-size: 14px;
        border-bottom: 1px solid rgba(0,100,200,0.6);
      }
      
      .chart {
        width: 100%;
        height: calc(100% - 35px);
      }
      
      #chart1 {
        top: 70px;
        left: 10px;
        width: 300px;
        height: 220px;
      }
      
      #chart2 {
        top: 70px;
        right: 10px;
        width: 300px;
        height: 220px;
      }
      
      #chart3 {
        bottom: 10px;
        left: 10px;
        width: 300px;
        height: 220px;
      }
      
      #chart4 {
        bottom: 10px;
        right: 10px;
        width: 300px;
        height: 220px;
      }
    </style>
  </head>
  <body>
    <div id="main-container">
      <!-- 顶部标题栏 -->
      <div id="header">
        <div id="title">白云电气设备数字孪生系统</div>
        <div id="controls">
          <button class="control-btn" id="overview-btn">总览</button>
          <button class="control-btn" id="tour-btn">漫游</button>
          <button class="control-btn" id="expand-btn">展开</button>
        </div>
      </div>
      
      <!-- 内容区域 -->
      <div id="content">
        <!-- Unity WebGL 容器 -->
        <div id="unity-container">
          <canvas id="unity-canvas" width=3840 height=2160 tabindex="-1"></canvas>
          <div id="unity-loading-bar">
            <div id="unity-logo"></div>
            <div id="unity-progress-bar-empty">
              <div id="unity-progress-bar-full"></div>
            </div>
          </div>
          <div id="unity-warning"></div>
        </div>
        
        <!-- 图表容器 -->
        <div id="chart1" class="chart-container">
          <div class="chart-title">设备运行状态</div>
          <div id="chart1-content" class="chart"></div>
        </div>
        
        <div id="chart2" class="chart-container">
          <div class="chart-title">温度监测</div>
          <div id="chart2-content" class="chart"></div>
        </div>
        
        <div id="chart3" class="chart-container">
          <div class="chart-title">电压监测</div>
          <div id="chart3-content" class="chart"></div>
        </div>
        
        <div id="chart4" class="chart-container">
          <div class="chart-title">系统负载</div>
          <div id="chart4-content" class="chart"></div>
        </div>
      </div>
    </div>
    
    <script>
      window.addEventListener("load", function () {
        if ("serviceWorker" in navigator) {
          navigator.serviceWorker.register("ServiceWorker.js");
        }
      });

      var container = document.querySelector("#unity-container");
      var canvas = document.querySelector("#unity-canvas");
      var loadingBar = document.querySelector("#unity-loading-bar");
      var progressBarFull = document.querySelector("#unity-progress-bar-full");
      var warningBanner = document.querySelector("#unity-warning");
      var unityInstance = null;

      // 显示消息横幅
      function unityShowBanner(msg, type) {
        function updateBannerVisibility() {
          warningBanner.style.display = warningBanner.children.length ? 'block' : 'none';
        }
        var div = document.createElement('div');
        div.innerHTML = msg;
        warningBanner.appendChild(div);
        if (type == 'error') div.style = 'background: red; padding: 10px;';
        else {
          if (type == 'warning') div.style = 'background: yellow; padding: 10px;';
          setTimeout(function() {
            warningBanner.removeChild(div);
            updateBannerVisibility();
          }, 5000);
        }
        updateBannerVisibility();
      }

      var buildUrl = "Build";
      var loaderUrl = buildUrl + "/webgl.loader.js";
      var config = {
        dataUrl: buildUrl + "/webgl.data.gz",
        frameworkUrl: buildUrl + "/webgl.framework.js.gz",
        codeUrl: buildUrl + "/webgl.wasm.gz",
        streamingAssetsUrl: "StreamingAssets",
        companyName: "DefaultCompany",
        productName: "BaiYunGroup",
        productVersion: "0.1",
        showBanner: unityShowBanner,
      };

      // 移动设备适配
      if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
        var meta = document.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, height=device-height, initial-scale=1.0, user-scalable=no, shrink-to-fit=yes';
        document.getElementsByTagName('head')[0].appendChild(meta);
      }

      loadingBar.style.display = "block";

      var script = document.createElement("script");
      script.src = loaderUrl;
      script.onload = () => {
        createUnityInstance(canvas, config, (progress) => {
          progressBarFull.style.width = 100 * progress + "%";
        }).then((instance) => {
          unityInstance = instance;
          loadingBar.style.display = "none";
      
          
          // 绑定按钮事件
          document.getElementById("overview-btn").addEventListener("click", function() {
            unityInstance.SendMessage("Main Camera", "SwitchToOverviewPosition");
          });
          
          document.getElementById("tour-btn").addEventListener("click", function() {
            unityInstance.SendMessage("Main Camera", "ToggleDeviceViewTour");
          });
          
          // 展开/收起按钮点击事件
          document.getElementById("expand-btn").addEventListener("click", function() {
            // 使用GameObject.Find查找具有DeviceController组件的对象
            unityInstance.SendMessage("Device", "ToggleExpand");
            
            // 切换按钮文本
            var expandBtn = document.getElementById("expand-btn");
            if (expandBtn.textContent === "展开") {
              expandBtn.textContent = "收起";
            } else {
              expandBtn.textContent = "展开";
            }
          });
          
          // 窗口大小变化时重新调整图表大小
          window.addEventListener('resize', function() {
            chart1.resize();
            chart2.resize();
            chart3.resize();
            chart4.resize();
          });
          
        }).catch((message) => {
          alert(message);
        });
      };
      document.body.appendChild(script);
      
      // 声明全局图表变量，以便在resize事件中访问
      var chart1, chart2, chart3, chart4;
      
      // 初始化图表
      function initCharts() {
        console.log('[图表初始化] 开始初始化所有图表');
        var chartContainers = document.querySelectorAll('.chart-container');
          chartContainers.forEach(function(container) {
            console.log('[图表初始化] 显示图表容器:', container.id);
            container.style.display = 'block';
          });
        // 设备运行状态图表
        console.log('[图表初始化] 开始初始化设备运行状态图表');
        chart1 = echarts.init(document.getElementById('chart1-content'));
        var option1 = {
          tooltip: {
            formatter: '{a} <br/>{b} : {c}%'
          },
          series: [{
            name: '设备状态',
            type: 'gauge',
            detail: { formatter: '{value}%' },
            data: [{ value: 87, name: '运行状态' }],
            axisLine: {
              lineStyle: {
                color: [[0.3, '#ff4500'], [0.7, '#ffca28'], [1, '#00e676']]
              }
            }
          }]
        };
        chart1.setOption(option1);
        console.log('[图表初始化] 设备运行状态图表初始化完成');
        
        // 温度监测图表
        console.log('[图表初始化] 开始初始化温度监测图表');
        chart2 = echarts.init(document.getElementById('chart2-content'));
        var option2 = {
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} °C'
            }
          },
          series: [{
            name: '温度',
            type: 'line',
            data: [28, 32, 36, 38, 35, 30, 28],
            areaStyle: {},
            itemStyle: {
              color: '#ff7043'
            },
            lineStyle: {
              width: 3
            }
          }]
        };
        chart2.setOption(option2);
        console.log('[图表初始化] 温度监测图表初始化完成');
        
        // 电压监测图表
        console.log('[图表初始化] 开始初始化电压监测图表');
        chart3 = echarts.init(document.getElementById('chart3-content'));
        var option3 = {
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} V'
            }
          },
          series: [{
            name: '电压',
            type: 'bar',
            data: [220, 218, 221, 219, 220, 221, 217],
            itemStyle: {
              color: '#29b6f6'
            }
          }]
        };
        chart3.setOption(option3);
        console.log('[图表初始化] 电压监测图表初始化完成');
        
        // 系统负载图表
        console.log('[图表初始化] 开始初始化系统负载图表');
        chart4 = echarts.init(document.getElementById('chart4-content'));
        var option4 = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            textStyle: {
              color: '#fff'
            }
          },
          series: [{
            name: '系统负载',
            type: 'pie',
            radius: '70%',
            data: [
              { value: 40, name: '设备A' },
              { value: 25, name: '设备B' },
              { value: 20, name: '设备C' },
              { value: 15, name: '设备D' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }]
        };
        chart4.setOption(option4);
        console.log('[图表初始化] 系统负载图表初始化完成');
        console.log('[图表初始化] 所有图表初始化完成');
        
        // 窗口大小变化时的重新调整已移至Unity加载完成后的回调中
      }
    </script>
  </body>
</html>
