using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 设备控制器 - 管理设备的所有交互功能
/// </summary>
public class DeviceController : MonoBehaviour
{
    [Header("设备组件")]
    [Tooltip("设备Logo对象，点击后触发展开/收回")]
    public Transform deviceLogo;

    [Tooltip("需要展开/收回的元器件列表")]
    public List<Transform> deviceComponents = new List<Transform>();

    [Tooltip("设备开关对象")]
    public Transform deviceSwitch;

    [Tooltip("设备门对象列表")]
    public List<Transform> deviceDoors = new List<Transform>();

    [Tooltip("进水管对象 - DN50-4.STEP-1.001")]
    public Transform inletPipe;

    [Tooltip("出水管对象 - DN50-4.STEP-1")]
    public Transform outletPipe;

    [Tooltip("状态显示器对象列表")]
    public List<Renderer> statusDisplays = new List<Renderer>();

    [Tooltip("数据显示器对象列表")]
    public List<Canvas> dataDisplays = new List<Canvas>();

    [Header("动画设置")]
    [Tooltip("元器件展开/收回动画时间")]
    public float expandDuration = 1.5f;

    [Tooltip("元器件展开时的基础距离（单位：米）")]
    public float baseExpandDistance = 0.5f;

    [Tooltip("元器件展开距离的缩放系数，影响基于组件到中心点距离的额外展开量")]
    public float expandDistanceScale = 1.0f;

    [Tooltip("元器件展开后的旋转速度")]
    public float rotationSpeed = 10.0f;

    [Header("效果设置")]
    [Tooltip("水流动画材质")]
    public Material waterFlowMaterial;

    [Tooltip("状态显示器闪烁频率")]
    public float blinkFrequency = 2.0f;

    [Tooltip("状态显示器正常颜色")]
    public Color normalColor = Color.green;

    [Tooltip("状态显示器警告颜色")]
    public Color warningColor = Color.yellow;

    [Tooltip("状态显示器错误颜色")]
    public Color errorColor = Color.red;

    // 私有变量
    private bool isExpanded = false;
    private bool _isSwitchOn = false;
    private bool isDoorOpen = false;
    private bool isWaterFlowing = false;
    
    /// <summary>
    /// 获取设备开关状态
    /// </summary>
    public bool isSwitchOn { get { return _isSwitchOn; } }
    private Dictionary<Transform, Vector3> originalPositions = new Dictionary<Transform, Vector3>();
    private Dictionary<Transform, Quaternion> originalRotations = new Dictionary<Transform, Quaternion>();
    private Dictionary<Transform, Vector3> expandedPositions = new Dictionary<Transform, Vector3>();
    private Vector3 doorClosedRotation;
    private Vector3 doorOpenRotation;
    private List<Material> statusDisplayMaterials = new List<Material>();
    private List<Coroutine> activeCoroutines = new List<Coroutine>();
    private Coroutine rotationCoroutine;

    [Tooltip("设备整体中心点 - 用于元器件旋转")]
    private Vector3 deviceCenterPoint;

    // 初始化
    void Start()
    {
        // 检查关键组件是否正确设置
        CheckKeyComponents();

        // 计算设备整体中心点
        CalculateDeviceCenterPoint();

        // 记录所有元器件的初始位置和旋转
        foreach (Transform component in deviceComponents)
        {
            if (component != null)
            {
                originalPositions[component] = component.localPosition;
                originalRotations[component] = component.localRotation;

                // 计算展开位置 - 根据组件相对于中心点的位置确定展开方向
                Vector3 relativePosition = component.position - transform.position;
                Vector3 directionFromCenter = Vector3.zero;

                // 计算从中心点到组件的径向方向向量（标准化）
                Vector3 radialDirection = (relativePosition);
                // 只保留X、Z分量，Y设为0
                // radialDirection.y = 0f;
                radialDirection = radialDirection.normalized;
                // 使用径向方向作为展开方向

                // 计算组件到中心点的距离
                float distanceToCenter = Vector3.Distance(component.position, deviceCenterPoint);

                // 根据距离动态调整展开距离
                // 基础距离 + 基于组件到中心点距离的额外展开量
                float dynamicExpandDistance = baseExpandDistance + (distanceToCenter * expandDistanceScale);

                // 在主坐标系（世界坐标系）下计算目标点
                Vector3 worldTargetPosition = component.position + radialDirection * dynamicExpandDistance;
                // 将目标点转换为组件父对象的局部坐标
                Vector3 localTargetPosition = component.parent.InverseTransformPoint(worldTargetPosition);
                expandedPositions[component] = localTargetPosition;
            }
        }

        // 获取所有状态显示器的材质
        foreach (Renderer display in statusDisplays)
        {
            if (display != null)
            {
                // 创建材质实例以便单独控制
                Material[] materials = display.materials;
                foreach (Material mat in materials)
                {
                    statusDisplayMaterials.Add(mat);
                }
                display.materials = materials;
            }
        }

        // 检查水管对象
        CheckWaterPipes();

        // 设置初始状态
        SetWaterFlow(false);
        SetStatusDisplayColor(normalColor);

        // 初始化数据显示器
        UpdateDataDisplayText();
    }

    // 水流方向枚举
    public enum WaterFlowDirection
    {
        RightToLeft,  // 从右向左
        LeftToRight,  // 从左向右
        TopToBottom,  // 从上往下
        BottomToTop   // 从下往上
    }

    // 水管结构，包含渲染器和流向
    [System.Serializable]
    public class WaterPipe
    {
        public Renderer pipeRenderer;
        public WaterFlowDirection flowDirection = WaterFlowDirection.TopToBottom; // 默认从上往下

        [HideInInspector]
        public Material instanceMaterial; // 实例化的材质
    }

    // 存储所有水管对象的列表
    [Tooltip("水管列表，可以为每个水管设置不同的流向")]
    public List<WaterPipe> waterPipes = new List<WaterPipe>();

    /// <summary>
    /// 检查水管对象并初始化水流材质
    /// </summary>
    private void CheckWaterPipes()
    {
        // 直接通过Layer查找所有水管对象（Water层）
        int waterLayer = LayerMask.NameToLayer("Water");
        if (waterLayer == -1)
        {
            Debug.LogError("未找到名为'Water'的Layer，请确保已创建该Layer并将水管对象放置在此Layer中");
            return;
        }

        // 如果水管列表为空，则自动查找并添加水管
        if (waterPipes.Count == 0)
        {
            // 查找场景中所有在Water层的游戏对象
            GameObject[] waterObjects = FindObjectsOfType<GameObject>().Where(go => go.layer == waterLayer).ToArray();
            Debug.Log($"在Water层找到 {waterObjects.Length} 个水管对象");

            // 获取所有水管的Renderer组件
            foreach (GameObject waterObject in waterObjects)
            {
                Renderer renderer = waterObject.GetComponent<Renderer>();
                if (renderer != null)
                {
                    // 创建新的水管对象并添加到列表
                    WaterPipe pipe = new WaterPipe();
                    pipe.pipeRenderer = renderer;

                    // 根据水管名称或位置自动判断流向
                    // 这里只是一个简单的示例，实际项目中可能需要更复杂的逻辑
                    string pipeName = waterObject.name.ToLower();
                    if (pipeName.Contains("vertical") || pipeName.Contains("down"))
                    {
                        pipe.flowDirection = WaterFlowDirection.TopToBottom;
                    }
                    else if (pipeName.Contains("up"))
                    {
                        pipe.flowDirection = WaterFlowDirection.BottomToTop;
                    }
                    else if (pipeName.Contains("left"))
                    {
                        pipe.flowDirection = WaterFlowDirection.RightToLeft;
                    }
                    else if (pipeName.Contains("right"))
                    {
                        pipe.flowDirection = WaterFlowDirection.LeftToRight;
                    }

                    waterPipes.Add(pipe);
                    Debug.Log($"添加水管: {waterObject.name}, 流向: {pipe.flowDirection}");
                }
                else
                {
                    Debug.LogWarning($"水管对象 {waterObject.name} 缺少Renderer组件");
                }
            }
        }
        else
        {
            // 验证已配置的水管列表
            for (int i = waterPipes.Count - 1; i >= 0; i--)
            {
                if (waterPipes[i].pipeRenderer == null)
                {
                    Debug.LogWarning($"水管列表中第 {i} 项的渲染器为空，已移除");
                    waterPipes.RemoveAt(i);
                }
            }
        }

        // 检查水流材质
        if (waterFlowMaterial == null)
        {
            Debug.LogWarning("水流材质未设置，请在Inspector中设置水流材质");
            return;
        }

        // 为每个水管创建材质实例以便单独控制
        foreach (WaterPipe pipe in waterPipes)
        {
            pipe.instanceMaterial = new Material(waterFlowMaterial);

            // 检查材质是否有必要的属性
            if (!pipe.instanceMaterial.HasProperty("_MainTex"))
            {
                Debug.LogWarning("水流材质缺少_MainTex属性，请确保使用正确的着色器");
                continue;
            }

            // 应用材质实例到水管
            if (pipe.pipeRenderer != null)
            {
                pipe.pipeRenderer.material = pipe.instanceMaterial;
            }
        }

        // 如果设备开启，应用水流材质到所有水管
        if (isSwitchOn)
        {
            ApplyWaterFlowMaterial();
        }
    }

    void Update()
    {
        // 检测点击事件
        if (Input.GetMouseButtonDown(0))
        {
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            RaycastHit hit;

            if (Physics.Raycast(ray, out hit))
            {
                // 输出点击到的对象信息，帮助调试
                Debug.Log($"点击到对象: {hit.transform.name}, 路径: {GetTransformPath(hit.transform)}");

                // 检查deviceSwitch引用是否正确
                if (deviceSwitch == null)
                {
                    Debug.LogError("deviceSwitch引用未设置，请在Inspector中设置开关引用");
                }
                else
                {
                    Debug.Log($"当前deviceSwitch引用: {deviceSwitch.name}, 路径: {GetTransformPath(deviceSwitch)}");
                }

                // 点击Logo触发展开/收回
                if (hit.transform == deviceLogo)
                {
                    Debug.Log("点击到Logo，触发ToggleExpand()");
                    ToggleExpand();
                }

                // 点击开关
                if (hit.transform == deviceSwitch)
                {
                    Debug.Log("点击到开关，触发ToggleSwitch()");
                    ToggleSwitch();
                }

                // 点击门 - 修改为检查deviceDoors列表中的任意门
                if (deviceDoors != null && deviceDoors.Contains(hit.transform))
                {
                    Debug.Log("点击到门，触发ToggleDoor()");
                    ToggleDoor();
                }
            }
        }

        // 确保Canvas始终保持正确的朝向（不面向屏幕）
        UpdateCanvasOrientation();
    }

    /// <summary>
    /// 更新Canvas的朝向，确保它们不会随着相机旋转而旋转
    /// </summary>
    private void UpdateCanvasOrientation()
    {
        foreach (Canvas canvas in dataDisplays)
        {
            if (canvas == null) continue;

            // 获取父对象
            Transform parent = canvas.transform.parent;
            if (parent == null) continue;

            // 确保Canvas的旋转与父对象一致，而不是面向相机
            if (canvas.transform.rotation != parent.rotation)
            {
                canvas.transform.rotation = parent.rotation;
            }
        }
    }

    /// <summary>
    /// 切换设备展开/收回状态
    /// </summary>
    public void ToggleExpand()
    {
        // 在展开设备前，确保开关处于关闭状态
        if (!isExpanded && _isSwitchOn)
        {
            Debug.Log("展开设备前自动关闭开关");
            ToggleSwitch(); // 关闭开关
        }

        isExpanded = !isExpanded;

        // 停止之前的动画
        StopAllCoroutines();
        activeCoroutines.Clear();

        if (isExpanded)
        {
            // 展开设备 - 直接执行展开动画
            foreach (Transform component in deviceComponents)
            {
                if (component != null)
                {
                    Vector3 targetPosition = expandedPositions[component];
                    Coroutine coroutine = StartCoroutine(AnimateComponentPosition(component, targetPosition, expandDuration));
                    activeCoroutines.Add(coroutine);
                }
            }

            // 启动旋转动画
            rotationCoroutine = StartCoroutine(RotateComponents());
        }
        else
        {
            // 收回设备 - 使用协调协程确保正确的动画顺序
            StartCoroutine(CoordinateRetraction());
        }
    }

    /// <summary>
    /// 协调设备收回过程，确保先停止旋转，然后等待组件回到默认位置和旋转
    /// </summary>
    private IEnumerator CoordinateRetraction()
    {
        // 0. 在收回设备前，确保开关处于关闭状态
        if (_isSwitchOn)
        {
            Debug.Log("收回设备前自动关闭开关");
            ToggleSwitch(); // 关闭开关
        }

        // 1. 首先停止旋转
        if (rotationCoroutine != null)
        {
            StopCoroutine(rotationCoroutine);
            rotationCoroutine = null;
        }

        // 2. 将组件移回默认位置
        List<Coroutine> positionCoroutines = new List<Coroutine>();
        foreach (Transform component in deviceComponents)
        {
            if (component != null)
            {
                Vector3 targetPosition = originalPositions[component];
                Coroutine coroutine = StartCoroutine(AnimateComponentPosition(component, targetPosition, expandDuration));
                positionCoroutines.Add(coroutine);
            }
        }

        // 3. 等待所有位置动画完成
        foreach (Coroutine coroutine in positionCoroutines)
        {
            yield return coroutine;
        }

        // 4. 恢复所有组件的初始旋转
        foreach (Transform component in deviceComponents)
        {
            if (component != null && originalRotations.ContainsKey(component))
            {
                StartCoroutine(AnimateComponentRotation(component, originalRotations[component], expandDuration * 0.5f));
            }
        }

        // 5. 等待旋转动画完成
        yield return new WaitForSeconds(expandDuration * 0.5f);

        // 6. 完成收回过程
        Debug.Log("设备收回完成");
    }

    /// <summary>
    /// 切换设备开关状态
    /// </summary>
    public void ToggleSwitch()
    {
        Debug.Log("ToggleSwitch方法被调用");

        // 记录切换前的状态
        bool previousState = _isSwitchOn;
        _isSwitchOn = !_isSwitchOn;
        Debug.Log($"开关状态从 {previousState} 切换为 {_isSwitchOn}");

        // 可以添加开关的视觉反馈，如旋转或材质变化
        if (deviceSwitch != null)
        {
            Debug.Log($"更新开关视觉状态，旋转角度: {(_isSwitchOn ? -30f : -90f)}");
            deviceSwitch.localEulerAngles = new Vector3(
                _isSwitchOn ? -30f : -90f,
                deviceSwitch.localEulerAngles.z,
                deviceSwitch.localEulerAngles.y
            );
        }
        else
        {
            Debug.LogError("deviceSwitch引用为空，无法更新视觉状态");
        }

        // 根据开关状态控制其他组件
        SetWaterFlow(_isSwitchOn);

        // 根据开关状态自动控制门的开关
        ToggleDoor(_isSwitchOn); // 开关打开时自动打开门，关闭时自动关闭门

        // 更新数据显示器内容
        UpdateDataDisplayText();



        if (_isSwitchOn)
        {
            StartStatusDisplayBlink();
        }
        else
        {
            StopStatusDisplayBlink();
            SetStatusDisplayColor(normalColor);
        }
    }

    /// <summary>
    /// 设置门的开关状态
    /// </summary>
    /// <param name="open">是否打开门，true表示打开，false表示关闭</param>
    public void ToggleDoor(bool open)
    {
        isDoorOpen = open;

        if (deviceDoors != null)  // 检查deviceDoors列表
        {
            // 遍历列表，设置门的激活状态
            foreach (Transform door in deviceDoors)
            {
                if (door != null)
                {
                    door.gameObject.SetActive(!isDoorOpen); // 门对象激活状态与开门状态相反
                }
            }
        }
    }

    /// <summary>
    /// 切换门的开关状态
    /// </summary>
    public void ToggleDoor()
    {
        // 调用带参数的ToggleDoor方法，传入当前状态的反值
        ToggleDoor(!isDoorOpen);
    }

    /// <summary>
    /// 将水流材质应用到所有水管对象
    /// </summary>
    private void ApplyWaterFlowMaterial()
    {
        if (waterFlowMaterial == null || waterPipes.Count == 0)
        {
            return;
        }

        foreach (WaterPipe pipe in waterPipes)
        {
            if (pipe.pipeRenderer != null)
            {
                // 如果实例材质为空，创建一个新的实例
                if (pipe.instanceMaterial == null)
                {
                    pipe.instanceMaterial = new Material(waterFlowMaterial);
                }

                pipe.pipeRenderer.material = pipe.instanceMaterial;
                Debug.Log($"已将水流材质应用到水管: {pipe.pipeRenderer.gameObject.name}, 流向: {pipe.flowDirection}");
            }
        }
    }

    /// <summary>
    /// 设置水管流动状态
    /// </summary>
    public void SetWaterFlow(bool isFlowing)
    {
        isWaterFlowing = isFlowing;

        // 检查水管对象和材质是否存在
        if (waterPipes.Count == 0)
        {
            Debug.LogWarning("未找到水管对象，无法显示水流动画");
            return;
        }

        // 控制水流材质动画
        if (waterFlowMaterial != null)
        {
            // 确保水流材质应用到所有水管对象上
            ApplyWaterFlowMaterial();

            if (isFlowing)
            {
                // 启动水流动画 - 通过移动材质UV实现流动效果
                StartCoroutine(AnimateWaterFlow());
            }
            else
            {
                // 停止水流动画 - 使用协程实例而不是名称
                StopAllCoroutines(); // 确保停止所有协程
            }
        }
        else
        {
            Debug.LogWarning("水流材质未设置，无法显示水流动画");
        }

        // 可以在这里添加水流粒子效果控制
    }

    /// <summary>
    /// 设置状态显示器颜色
    /// </summary>
    public void SetStatusDisplayColor(Color color)
    {
        foreach (Material mat in statusDisplayMaterials)
        {
            mat.SetColor("_EmissionColor", color * 2f); // 发光强度加倍
            mat.color = color;
        }
    }

    /// <summary>
    /// 启动状态显示器闪烁效果
    /// </summary>
    public void StartStatusDisplayBlink()
    {
        StopStatusDisplayBlink();
        StartCoroutine(BlinkStatusDisplay());
    }

    /// <summary>
    /// 停止状态显示器闪烁效果
    /// </summary>
    public void StopStatusDisplayBlink()
    {
        StopCoroutine(nameof(BlinkStatusDisplay));
    }

    /// <summary>
    /// 更新数据显示器文本内容
    /// </summary>
    public void UpdateDataDisplayText()
    {
        if (dataDisplays.Count == 0)
        {
            Debug.LogWarning("未找到数据显示器，无法更新显示内容");
            return;
        }

        string displayText = isSwitchOn ? "设备已启动" : "设备待机";

        foreach (Canvas canvas in dataDisplays)
        {
            if (canvas != null)
            {
                // 查找Canvas下的Text组件和Image组件
                UnityEngine.UI.Text textComponent = canvas.GetComponentInChildren<UnityEngine.UI.Text>();
                UnityEngine.UI.Image backgroundImage = canvas.GetComponentInChildren<UnityEngine.UI.Image>();

                // 更新文本内容
                if (textComponent != null)
                {
                    textComponent.text = displayText;
                    Debug.Log($"更新数据显示器内容: {displayText}");
                }
                else
                {
                    Debug.LogWarning($"数据显示器Canvas '{canvas.name}' 中未找到Text组件");
                }

                // 更新背景颜色和透明度
                if (backgroundImage != null)
                {
                    // 根据开关状态设置背景色和透明度
                    Color color = _isSwitchOn ? Color.green : Color.green;
                    color.a = _isSwitchOn ? 1f : 0f; // 开启时完全不透明(1)，关闭时完全透明(0)
                    backgroundImage.color = color;
                }
                else
                {
                    Debug.LogWarning($"数据显示器Canvas '{canvas.name}' 中未找到Image组件");
                }
            }
        }
    }

    /// <summary>
    /// 获取Transform的完整路径，用于调试
    /// </summary>
    private string GetTransformPath(Transform transform)
    {
        if (transform == null)
            return "null";

        string path = transform.name;
        Transform parent = transform.parent;

        while (parent != null)
        {
            path = parent.name + "/" + path;
            parent = parent.parent;
        }

        return path;
    }



    /// <summary>
    /// 检查关键组件是否正确设置
    /// </summary>
    private void CheckKeyComponents()
    {
        // 检查设备Logo
        if (deviceLogo == null)
        {
            Debug.LogError("deviceLogo引用未设置，请在Inspector中设置Logo引用");
        }
        else if (deviceLogo.GetComponent<Collider>() == null)
        {
            Debug.LogError($"deviceLogo对象 '{deviceLogo.name}' 缺少Collider组件，无法被射线检测到");
        }

        // 检查设备开关
        if (deviceSwitch == null)
        {
            Debug.LogError("deviceSwitch引用未设置，请在Inspector中设置开关引用");
        }
        else
        {
            // 检查开关是否有碰撞器
            Collider switchCollider = deviceSwitch.GetComponent<Collider>();
            if (switchCollider == null)
            {
                Debug.LogError($"deviceSwitch对象 '{deviceSwitch.name}' 缺少Collider组件，无法被射线检测到");
                Debug.LogError("请为开关对象添加BoxCollider、SphereCollider或MeshCollider组件");
            }
            else
            {
                Debug.Log($"deviceSwitch对象 '{deviceSwitch.name}' 已配置碰撞器: {switchCollider.GetType().Name}");

                // 检查碰撞器是否启用
                if (!switchCollider.enabled)
                {
                    Debug.LogError($"deviceSwitch对象 '{deviceSwitch.name}' 的碰撞器已禁用，请启用它");
                }
            }
        }

        // 检查设备门
        if (deviceDoors == null || deviceDoors.Count == 0)
        {
            Debug.LogWarning("deviceDoors列表为空，门交互功能将不可用");
        }
        else
        {
            foreach (Transform door in deviceDoors)
            {
                if (door == null)
                {
                    Debug.LogWarning("deviceDoors列表中存在空引用，请检查");
                    continue;
                }

                if (door.GetComponent<Collider>() == null)
                {
                    Debug.LogWarning($"门对象 '{door.name}' 缺少Collider组件，无法被射线检测到");
                }
            }
        }
    }

    #region 动画协程

    /// <summary>
    /// 元器件位置动画
    /// </summary>
    private IEnumerator AnimateComponentPosition(Transform component, Vector3 targetPosition, float duration)
    {
        Vector3 startPosition = component.localPosition;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.Clamp01(elapsed / duration);
            t = Mathf.SmoothStep(0, 1, t); // 平滑动画曲线

            component.localPosition = Vector3.Lerp(startPosition, targetPosition, t);
            yield return null;
        }

        component.localPosition = targetPosition; // 确保最终位置精确
    }

    /// <summary>
    /// 计算设备整体中心点
    /// </summary>
    private void CalculateDeviceCenterPoint()
    {
        // 如果没有组件，使用当前对象位置作为中心点
        if (deviceComponents.Count == 0)
        {
            deviceCenterPoint = transform.position;
            return;
        }

        // 计算所有组件的平均位置作为设备中心点
        Vector3 sum = Vector3.zero;
        int validComponentCount = 0;

        foreach (Transform component in deviceComponents)
        {
            if (component != null)
            {
                sum += component.position;
                validComponentCount++;
            }
        }

        // 如果有有效组件，计算平均位置；否则使用当前对象位置
        if (validComponentCount > 0)
        {
            deviceCenterPoint = sum / validComponentCount;
        }
        else
        {
            deviceCenterPoint = transform.position;
        }

        Debug.Log($"设备中心点已计算: {deviceCenterPoint}");
    }

    /// <summary>
    /// 元器件旋转动画 - 在展开状态下围绕中心点旋转
    /// </summary>
    private IEnumerator RotateComponents()
    {

        while (isExpanded)
        {
            foreach (Transform component in deviceComponents)
            {
                if (component != null)
                {
                    // 使用计算好的设备整体中心点
                    Vector3 centerPoint = deviceCenterPoint;
                    Vector3 componentPosition = component.position;

                    // 计算旋转轴（使用Y轴作为旋转轴）
                    Vector3 rotationAxis = Vector3.up;

                    // 计算当前帧的旋转角度
                    float rotationAngle = rotationSpeed * Time.deltaTime;

                    // 围绕中心点旋转组件
                    component.RotateAround(centerPoint, rotationAxis, rotationAngle);
                }
            }

            yield return null;
        }

    }

    /// <summary>
    /// 元器件旋转动画 - 平滑过渡到目标旋转值
    /// </summary>
    private IEnumerator AnimateComponentRotation(Transform component, Quaternion targetRotation, float duration)
    {
        Quaternion startRotation = component.localRotation;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.Clamp01(elapsed / duration);
            t = Mathf.SmoothStep(0, 1, t); // 平滑动画曲线

            component.localRotation = Quaternion.Slerp(startRotation, targetRotation, t);
            yield return null;
        }

        component.localRotation = targetRotation; // 确保最终旋转精确
    }



    /// <summary>
    /// 水流动画
    /// </summary>
    private IEnumerator AnimateWaterFlow()
    {
        // 用于跟踪每个方向的UV偏移
        float offsetX = 0f;
        float offsetY = 0f;

        // 检查水管列表是否为空
        if (waterPipes.Count == 0)
        {
            Debug.LogWarning("水管列表为空，无法应用水流动画");
            yield break;
        }

        Debug.Log($"开始为 {waterPipes.Count} 个水管应用水流动画");

        // 确保每个水管的材质都已正确设置
        foreach (WaterPipe pipe in waterPipes)
        {
            if (pipe.pipeRenderer == null || pipe.instanceMaterial == null)
            {
                continue;
            }

            // 确保材质的主纹理属性存在
            if (!pipe.instanceMaterial.HasProperty("_MainTex"))
            {
                Debug.LogError($"水管 {pipe.pipeRenderer.gameObject.name} 的材质缺少_MainTex属性");
                continue;
            }

            // 设置材质为启用发光
            pipe.instanceMaterial.EnableKeyword("_EMISSION");
        }

        while (isWaterFlowing)
        {
            // 更新偏移值
            offsetX += Time.deltaTime * 0.5f; // 控制水平流动速度
            offsetY += Time.deltaTime * 0.5f; // 控制垂直流动速度

            // 循环UV偏移
            if (offsetX > 1f) offsetX -= 1f;
            if (offsetY > 1f) offsetY -= 1f;

            // 更新水流材质的UV偏移（仅在设备开启时）
            if (!isSwitchOn)
            {
                Debug.Log("设备已关闭，停止水流动画");
                yield break;
            }

            // 为每个水管根据其流向设置不同的UV偏移
            foreach (WaterPipe pipe in waterPipes)
            {
                if (pipe.pipeRenderer == null || pipe.instanceMaterial == null)
                {
                    continue;
                }

                // 根据流向设置UV偏移
                Vector2 uvOffset = Vector2.zero;

                switch (pipe.flowDirection)
                {
                    case WaterFlowDirection.RightToLeft:
                        uvOffset = new Vector2(0, offsetY); // 水平从右向左流动 - 纹理向右移动，视觉效果是水从右向左流
                        break;
                    case WaterFlowDirection.LeftToRight:
                        uvOffset = new Vector2(0, -offsetY); // 水平从左向右流动 - 纹理向左移动，视觉效果是水从左向右流
                        break;
                    case WaterFlowDirection.TopToBottom:
                        uvOffset = new Vector2(-offsetX, 0); // 垂直从上往下流动 - 纹理向下移动
                        break;
                    case WaterFlowDirection.BottomToTop:
                        uvOffset = new Vector2(offsetX, 0); // 垂直从下往上流动 - 纹理向上移动
                        break;
                }

                // 更新材质的UV偏移
                pipe.instanceMaterial.SetTextureOffset("_MainTex", uvOffset);

                // 可选：添加颜色变化以增强视觉效果
                Color flowColor = Color.Lerp(Color.blue, Color.cyan, Mathf.PingPong(Time.time * 0.5f, 1.0f));
                pipe.instanceMaterial.SetColor("_EmissionColor", flowColor * 0.5f);
            }

            yield return null;
        }

        // 重置所有材质状态
        foreach (WaterPipe pipe in waterPipes)
        {
            if (pipe.instanceMaterial != null)
            {
                pipe.instanceMaterial.SetTextureOffset("_MainTex", Vector2.zero);
            }
        }
    }

    /// <summary>
    /// 状态显示器闪烁动画
    /// </summary>
    private IEnumerator BlinkStatusDisplay()
    {
        bool isOn = true;

        while (isSwitchOn)
        {
            isOn = !isOn;

            // 在正常颜色和警告颜色之间切换
            Color targetColor = isOn ? warningColor : normalColor;
            SetStatusDisplayColor(targetColor);

            yield return new WaitForSeconds(1f / blinkFrequency);
        }
    }

    #endregion
}