/**
 * 白云电气设备数字孪生系统 - 主要JavaScript文件
 * 包含Unity初始化、事件绑定等核心功能
 */

// Unity实例全局变量
var unityInstance = null;

/**
 * 页面加载完成后的初始化
 */
window.addEventListener("load", function () {
  // 注册Service Worker
  if ("serviceWorker" in navigator) {
    navigator.serviceWorker.register("ServiceWorker.js");
  }
  
  // 初始化Unity
  initUnity();
});

/**
 * 初始化Unity WebGL
 */
function initUnity() {
  var container = document.querySelector("#unity-container");
  var canvas = document.querySelector("#unity-canvas");
  var loadingBar = document.querySelector("#unity-loading-bar");
  var progressBarFull = document.querySelector("#unity-progress-bar-full");
  var warningBanner = document.querySelector("#unity-warning");

  // Unity构建配置
  var buildUrl = "Build";
  var loaderUrl = buildUrl + "/webgl.loader.js";
  var config = {
    dataUrl: buildUrl + "/webgl.data.gz",
    frameworkUrl: buildUrl + "/webgl.framework.js.gz",
    codeUrl: buildUrl + "/webgl.wasm.gz",
    streamingAssetsUrl: "StreamingAssets",
    companyName: "DefaultCompany",
    productName: "BaiYunGroup",
    productVersion: "0.1",
    showBanner: unityShowBanner,
  };

  // 移动设备适配
  if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
    var meta = document.createElement('meta');
    meta.name = 'viewport';
    meta.content = 'width=device-width, height=device-height, initial-scale=1.0, user-scalable=no, shrink-to-fit=yes';
    document.getElementsByTagName('head')[0].appendChild(meta);
  }

  // 显示加载条
  loadingBar.style.display = "block";

  // 加载Unity
  var script = document.createElement("script");
  script.src = loaderUrl;
  script.onload = () => {
    createUnityInstance(canvas, config, (progress) => {
      progressBarFull.style.width = 100 * progress + "%";
    }).then((instance) => {
      unityInstance = instance;
      loadingBar.style.display = "none";
      
      // Unity加载完成后初始化图表和绑定事件
      onUnityLoaded();
      
    }).catch((message) => {
      alert(message);
    });
  };
  document.body.appendChild(script);
}

/**
 * Unity加载完成后的回调函数
 */
function onUnityLoaded() {
  // 初始化图表
  initCharts();
  
  // 绑定控制按钮事件
  bindControlEvents();
  
  // 绑定窗口大小变化事件
  window.addEventListener('resize', function() {
    resizeAllCharts();
  });
}

/**
 * 绑定控制按钮事件
 */
function bindControlEvents() {
  // 总览按钮
  document.getElementById("overview-btn").addEventListener("click", function() {
    if (unityInstance) {
      unityInstance.SendMessage("Main Camera", "SwitchToOverviewPosition");
    }
  });
  
  // 漫游按钮
  document.getElementById("tour-btn").addEventListener("click", function() {
    if (unityInstance) {
      unityInstance.SendMessage("Main Camera", "ToggleDeviceViewTour");
    }
  });
  
  // 展开/收起按钮
  document.getElementById("expand-btn").addEventListener("click", function() {
    if (unityInstance) {
      // 使用GameObject.Find查找具有DeviceController组件的对象
      unityInstance.SendMessage("Device", "ToggleExpand");
      
      // 切换按钮文本
      var expandBtn = document.getElementById("expand-btn");
      if (expandBtn.textContent === "展开") {
        expandBtn.textContent = "收起";
      } else {
        expandBtn.textContent = "展开";
      }
    }
  });
}

/**
 * 显示Unity消息横幅
 * @param {string} msg - 消息内容
 * @param {string} type - 消息类型 ('error', 'warning', 或其他)
 */
function unityShowBanner(msg, type) {
  var warningBanner = document.querySelector("#unity-warning");
  
  function updateBannerVisibility() {
    warningBanner.style.display = warningBanner.children.length ? 'block' : 'none';
  }
  
  var div = document.createElement('div');
  div.innerHTML = msg;
  warningBanner.appendChild(div);
  
  if (type == 'error') {
    div.style = 'background: red; padding: 10px;';
  } else {
    if (type == 'warning') {
      div.style = 'background: yellow; padding: 10px;';
    }
    setTimeout(function() {
      warningBanner.removeChild(div);
      updateBannerVisibility();
    }, 5000);
  }
  updateBannerVisibility();
}
