# 白云电气设备数字孪生系统 - 高保真原型

## 项目概述

这是白云电气设备数字孪生监控系统的高保真原型，展示了一个具有科技感的3D可视化大屏界面。该原型集成了Unity3D WebGL、实时数据监控、响应式设计和自定义CSS动画效果。

## 功能特性

### 🎨 视觉设计
- **科技感UI**：深色主题配合蓝色科技风格
- **霓虹灯效果**：发光文字和边框动画
- **渐变背景**：多层次渐变和粒子效果
- **响应式布局**：适配不同屏幕尺寸

### 🔧 核心功能
- **3D模型展示**：Unity WebGL集成的设备模型
- **实时数据监控**：电压、温度、负载率等关键指标
- **设备状态管理**：运行状态、告警、故障显示
- **交互控制**：视角切换、设备展开、自动漫游

### 📊 数据可视化
- **ECharts图表**：电压趋势、温度监测图表
- **实时更新**：模拟MODBUS数据源的实时数据流
- **多时间范围**：1小时、6小时、24小时数据展示
- **状态指示器**：设备运行状态的可视化展示

### ✨ 动画效果
- **页面加载动画**：渐入效果和加载进度条
- **悬停效果**：按钮和卡片的交互反馈
- **波纹效果**：点击按钮的水波纹动画
- **数据流动**：模拟数据传输的流动效果

## 文件结构

```
docs/高保真原型/
├── index.html              # 主页面文件
├── styles/                 # 样式文件目录
│   ├── main.css            # 主要样式和布局
│   ├── components.css      # 组件样式
│   └── animations.css      # 动画效果
├── scripts/                # JavaScript文件目录
│   ├── main.js             # 主要逻辑和交互
│   ├── charts.js           # 图表管理
│   └── unity-integration.js # Unity集成
└── README.md               # 说明文档
```

## 技术栈

- **前端框架**：原生HTML5 + CSS3 + JavaScript
- **图表库**：ECharts 5.4.3
- **图标库**：Font Awesome 6.0.0
- **3D引擎**：Unity WebGL（模拟集成）
- **动画**：CSS3 Animations + Transitions

## 使用说明

### 1. 打开原型
直接在浏览器中打开 `index.html` 文件即可查看原型效果。

### 2. 界面布局

#### 顶部导航栏
- **系统标题**：显示系统名称和Logo
- **实时时间**：当前系统时间显示
- **状态指示器**：系统在线状态和用户数量

#### 左侧控制面板
- **视角控制**：
  - 总览视角：切换到设备全景视图
  - 自动漫游：启动/停止自动视角漫游
  - 设备展开：展开/收起设备组件
- **设备状态列表**：显示各设备的实时运行状态

#### 中央3D展示区域
- **Unity WebGL容器**：显示3D设备模型
- **设备状态指示器**：在3D场景中标记设备位置和状态
- **场景工具栏**：重置视角、全屏、截图等功能

#### 右侧数据面板
- **关键指标卡片**：电压、温度、负载率实时数值
- **趋势图表**：电压和温度的历史趋势
- **时间范围切换**：1H/6H/24H数据查看

#### 底部状态栏
- **系统信息**：版本号、更新时间
- **连接状态**：网络连接和信号强度
- **版权信息**：公司信息

### 3. 交互功能

#### 控制按钮
- 点击左侧控制按钮可以模拟Unity场景的视角变化
- 按钮具有波纹点击效果和悬停动画

#### 设备交互
- 在3D场景中点击设备状态指示器查看设备详情
- 悬停显示设备名称和状态信息

#### 图表交互
- 点击图表上方的时间范围按钮切换数据显示
- 图表支持鼠标悬停查看具体数值

#### 键盘快捷键
- `ESC`：关闭模态框
- `F11`：切换全屏模式

### 4. 数据模拟

原型中的所有数据都是模拟生成的：
- **实时数据**：每2秒更新一次指标卡片数值
- **图表数据**：每5秒更新一次图表数据点
- **设备状态**：随机模拟设备状态变化（低概率）
- **告警系统**：当设备状态异常时自动弹出告警框

## 设计特色

### 科技感视觉效果
1. **色彩方案**：以深蓝色为主色调，配合青色和白色
2. **发光效果**：文字、边框、按钮的霓虹灯效果
3. **渐变背景**：多层次径向渐变营造科技氛围
4. **粒子效果**：背景粒子动画模拟数据流动

### 响应式设计
- 支持1200px以上的大屏显示
- 在较小屏幕上自动调整布局为垂直排列
- 图表和组件自适应容器大小

### 动画系统
- **页面加载**：分层渐入动画，营造专业感
- **交互反馈**：悬停、点击、状态变化的流畅过渡
- **数据可视化**：图表更新和数据流动的动画效果
- **状态指示**：设备状态的脉冲和闪烁动画

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 性能优化

- 使用CSS3硬件加速动画
- 图表按需更新，避免频繁重绘
- 响应式图片和矢量图标
- 优化的CSS选择器和动画性能

## 扩展说明

### 与实际Unity项目集成
1. 替换 `unity-integration.js` 中的模拟代码
2. 配置正确的Unity WebGL构建路径
3. 实现真实的Unity消息通信

### 与MODBUS数据源集成
1. 替换模拟数据生成函数
2. 实现WebSocket或HTTP API数据获取
3. 配置实时数据更新频率

### 自定义主题
1. 修改 `main.css` 中的CSS变量
2. 调整颜色方案和动画效果
3. 更新图表配色方案

## 联系信息

- 项目：白云电气设备数字孪生系统
- 版本：v1.0.0 高保真原型
- 更新：2024年1月

---

*此原型仅用于演示和设计验证，实际部署时需要集成真实的Unity WebGL项目和数据源。*
