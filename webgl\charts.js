/**
 * 白云电气设备数字孪生系统 - 图表配置文件
 * 包含所有图表的配置和初始化逻辑
 */

// 声明全局图表变量，以便在resize事件中访问
var chart1, chart2, chart3, chart4;

/**
 * 初始化所有图表
 * 在Unity加载完成后调用此函数
 */
function initCharts() {
  console.log('[图表初始化] 开始初始化所有图表');
  
  // 显示所有图表容器
  var chartContainers = document.querySelectorAll('.chart-container');
  chartContainers.forEach(function(container) {
    console.log('[图表初始化] 显示图表容器:', container.id);
    container.style.display = 'block';
  });
  
  // 初始化设备运行状态图表
  initDeviceStatusChart();
  
  // 初始化温度监测图表
  initTemperatureChart();
  
  // 初始化电压监测图表
  initVoltageChart();
  
  // 初始化系统负载图表
  initSystemLoadChart();
  
  console.log('[图表初始化] 所有图表初始化完成');
}

/**
 * 初始化设备运行状态图表（仪表盘）
 */
function initDeviceStatusChart() {
  console.log('[图表初始化] 开始初始化设备运行状态图表');
  
  chart1 = echarts.init(document.getElementById('chart1-content'));
  var option1 = {
    tooltip: {
      formatter: '{a} <br/>{b} : {c}%'
    },
    series: [{
      name: '设备状态',
      type: 'gauge',
      detail: { formatter: '{value}%' },
      data: [{ value: 87, name: '运行状态' }],
      axisLine: {
        lineStyle: {
          color: [[0.3, '#ff4500'], [0.7, '#ffca28'], [1, '#00e676']]
        }
      }
    }]
  };
  chart1.setOption(option1);
  
  console.log('[图表初始化] 设备运行状态图表初始化完成');
}

/**
 * 初始化温度监测图表（折线图）
 */
function initTemperatureChart() {
  console.log('[图表初始化] 开始初始化温度监测图表');
  
  chart2 = echarts.init(document.getElementById('chart2-content'));
  var option2 = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} °C'
      }
    },
    series: [{
      name: '温度',
      type: 'line',
      data: [28, 32, 36, 38, 35, 30, 28],
      areaStyle: {},
      itemStyle: {
        color: '#ff7043'
      },
      lineStyle: {
        width: 3
      }
    }]
  };
  chart2.setOption(option2);
  
  console.log('[图表初始化] 温度监测图表初始化完成');
}

/**
 * 初始化电压监测图表（柱状图）
 */
function initVoltageChart() {
  console.log('[图表初始化] 开始初始化电压监测图表');
  
  chart3 = echarts.init(document.getElementById('chart3-content'));
  var option3 = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} V'
      }
    },
    series: [{
      name: '电压',
      type: 'bar',
      data: [220, 218, 221, 219, 220, 221, 217],
      itemStyle: {
        color: '#29b6f6'
      }
    }]
  };
  chart3.setOption(option3);
  
  console.log('[图表初始化] 电压监测图表初始化完成');
}

/**
 * 初始化系统负载图表（饼图）
 */
function initSystemLoadChart() {
  console.log('[图表初始化] 开始初始化系统负载图表');
  
  chart4 = echarts.init(document.getElementById('chart4-content'));
  var option4 = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        color: '#fff'
      }
    },
    series: [{
      name: '系统负载',
      type: 'pie',
      radius: '70%',
      data: [
        { value: 40, name: '设备A' },
        { value: 25, name: '设备B' },
        { value: 20, name: '设备C' },
        { value: 15, name: '设备D' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
  chart4.setOption(option4);
  
  console.log('[图表初始化] 系统负载图表初始化完成');
}

/**
 * 调整所有图表大小
 * 在窗口大小变化时调用
 */
function resizeAllCharts() {
  if (chart1) chart1.resize();
  if (chart2) chart2.resize();
  if (chart3) chart3.resize();
  if (chart4) chart4.resize();
}
